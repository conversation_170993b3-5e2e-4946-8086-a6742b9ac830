/*
 * Copyright 1999-2023 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.persistence.repository.embedded.hook;

import com.alibaba.nacos.consistency.entity.WriteRequest;

/**
 * Embedded storage apply hook.
 *
 * <p>Async Hook after embedded storage apply raft log.</p>
 *
 * <AUTHOR>
 */
@SuppressWarnings("PMD.AbstractClassShouldStartWithAbstractNamingRule")
public abstract class EmbeddedApplyHook {
    
    protected EmbeddedApplyHook() {
        EmbeddedApplyHookHolder.getInstance().register(this);
    }
    
    /**
     * Called after apply finished.
     *
     * @param log raft log
     */
    public abstract void afterApply(WriteRequest log);
}
