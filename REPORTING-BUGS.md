# How to report bugs

If any part of the Nacos project has bugs or documentation mistakes, please let us know by [opening an issue][Nacos-issue]. We treat bugs and mistakes very seriously and believe no issue is too small, anyone is implement. Before creating a bug report, please check that an issue reporting the same problem does not already exist.

To make the bug report accurate and easy to understand, please try to create bug reports that are:

- Specific. Include as many details as possible: which version, what environment, what configuration, etc. If the bug is related to running the Nacos server, please attach the Nacos log (the starting log with Nacos configuration is especially important).

- Reproducible. Include the steps to reproduce the problem. We understand some issues might be hard to reproduce, please includes the steps that might lead to the problem. If possible, please attach the affected Nacos data dir and stack strace to the bug report.

- Unique. Do not duplicate the existing bug report.


It may be worthwhile to read [<PERSON><PERSON>’s article on filing good bug reports][filing-good-bugs] before creating a bug report.

We might ask for further information to locate a bug. A duplicated bug report will be closed.

[etcd-issue]: https://github.com/etcd-io/etcd/issues/new
[filing-good-bugs]: http://fantasai.inkedblade.net/style/talks/filing-good-bugs/


# 如何提交错误报告

如果Nacos项目的任何部分存在问题或文档问题，请通过[opening an issue][Nacos-issue]告诉我们。我们非常认真地对待错误和缺陷，在产品面前没有不重要的问题。不过在创建错误报告之前，请检查是否存在报告相同问题的issues。

为了使错误报告准确且易于理解，请尝试创建以下错误报告：

- 具体到细节。包括尽可能多的细节：哪个版本，什么环境，什么配置等。如果错误与运行Nacos服务器有关，请附加Nacos日志（具有Nacos配置的起始日志尤为重要）。

- 可复现。包括重现问题的步骤。我们理解某些问题可能难以重现，请包括可能导致问题的步骤。如果可能，请将受影响的Nacos数据目录和堆栈strace附加到错误报告中。

- 不重复。不要复制现有的错误报告。

在创建错误报告之前，最好阅读下[Elika Etemad关于提交好错误报告的文章] [归档好错误]，相信 会给你启发。

我们可能会要求您提供更多信息以查找错误。将关闭重复的错误报告。

[etcd-issue]：https：//github.com/etcd-io/etcd/issues/new
[filing-good-bugs]：http：//fantasai.inkedblade.net/style/talks/filing-good-bugs/
