<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 1999-2018 Alibaba Group Holding Ltd.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<assembly>
    <id>cfg</id>
    <includeBaseDirectory>false</includeBaseDirectory>
    <formats>
        <format>dir</format>
        <format>tar.gz</format>
        <format>zip</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>../</directory>
            <includes>
                <include>README.md</include>
            </includes>
        </fileSet>

        <fileSet>
            <includes>
                <include>conf/**</include>
                <include>benchmark/*</include>
            </includes>
        </fileSet>

        <fileSet>
            <includes>
                <include>bin/*</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>

    <files>
        <file>
            <source>LICENSE-BIN</source>
            <destName>LICENSE</destName>
        </file>
        <file>
            <source>NOTICE-BIN</source>
            <destName>NOTICE</destName>
        </file>
    </files>

    <moduleSets>
        <moduleSet>
            <useAllReactorProjects>true</useAllReactorProjects>
            <includes>
                <include>com.alibaba.nacos:nacos-config</include>
            </includes>
            <binaries>
                <outputDirectory>lib/</outputDirectory>
                <unpack>false</unpack>
                <dependencySets>
                    <dependencySet>
                        <outputDirectory>lib/</outputDirectory>
                    </dependencySet>
                </dependencySets>
            </binaries>
        </moduleSet>
    </moduleSets>
</assembly>
