<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 1999-2018 Alibaba Group Holding Ltd.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-all</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>nacos-distribution</artifactId>
    <name>nacos-distribution ${project.version}</name>
    <packaging>pom</packaging>

    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>nacos-console</artifactId>
        </dependency>
    </dependencies>
    
    <profiles>
        <profile>
            <id>release-config</id>
            <dependencies>
                <dependency>
                    <groupId>${project.groupId}</groupId>
                    <artifactId>nacos-config</artifactId>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release-config</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>release-config.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>false</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>acm</finalName>
            </build>
        </profile>
        <profile>
            <id>release-naming</id>
            <dependencies>
                <dependency>
                    <groupId>${project.groupId}</groupId>
                    <artifactId>nacos-naming</artifactId>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release-naming</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>release-naming.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>false</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>ans</finalName>
            </build>
        </profile>
        <profile>
            <id>release-address</id>
            <build>
                <finalName>nacos-address</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <descriptors>
                                <descriptor>release-address.xml</descriptor>
                            </descriptors>
                        </configuration>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release-client</id>
            <dependencies>
                <dependency>
                    <groupId>${project.groupId}</groupId>
                    <artifactId>nacos-client</artifactId>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release-client</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>release-client.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>false</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>nacos-client</finalName>
            </build>
        </profile>
        <profile>
            <id>release-core</id>
            <dependencies>
                <dependency>
                    <groupId>${project.groupId}</groupId>
                    <artifactId>nacos-core</artifactId>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release-core</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>release-core.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>false</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>nacos-core</finalName>
            </build>
        </profile>
        <profile>
            <id>release-nacos</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <descriptors>
                                <descriptor>release-nacos.xml</descriptor>
                            </descriptors>
                            <tarLongFileMode>posix</tarLongFileMode>
                        </configuration>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>install</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <finalName>nacos</finalName>
            </build>
        </profile>
    </profiles>
</project>
