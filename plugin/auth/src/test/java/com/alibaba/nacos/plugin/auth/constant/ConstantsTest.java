/*
 * Copyright 1999-2021 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.plugin.auth.constant;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ConstantsTest {
    
    @Test
    public void testConstantsForAuth() {
        assertEquals("nacos.core.auth.enabled", Constants.Auth.NACOS_CORE_AUTH_ENABLED);
        assertEquals("nacos.core.auth.system.type", Constants.Auth.NACOS_CORE_AUTH_SYSTEM_TYPE);
        assertEquals("nacos.core.auth.caching.enabled", Constants.Auth.NACOS_CORE_AUTH_CACHING_ENABLED);
        assertEquals("nacos.core.auth.server.identity.key", Constants.Auth.NACOS_CORE_AUTH_SERVER_IDENTITY_KEY);
        assertEquals("nacos.core.auth.server.identity.value", Constants.Auth.NACOS_CORE_AUTH_SERVER_IDENTITY_VALUE);
        assertEquals("nacos.core.auth.enable.userAgentAuthWhite",
                Constants.Auth.NACOS_CORE_AUTH_ENABLE_USER_AGENT_AUTH_WHITE);
    }
    
    @Test
    public void testConstantsForResource() {
        assertEquals(":", Constants.Resource.SPLITTER);
        assertEquals("*", Constants.Resource.ANY);
        assertEquals("action", Constants.Resource.ACTION);
        assertEquals("requestClass", Constants.Resource.REQUEST_CLASS);
    }
    
    @Test
    public void testConstantsForIdentity() {
        assertEquals("identity_id", Constants.Identity.IDENTITY_ID);
        assertEquals("X-Real-IP", Constants.Identity.X_REAL_IP);
        assertEquals("remote_ip", Constants.Identity.REMOTE_IP);
    }
    
    @Test
    public void testConstantsForSignType() {
        assertEquals("naming", SignType.NAMING);
        assertEquals("config", SignType.CONFIG);
        assertEquals("console", SignType.CONSOLE);
        assertEquals("specified", SignType.SPECIFIED);
    }
}
