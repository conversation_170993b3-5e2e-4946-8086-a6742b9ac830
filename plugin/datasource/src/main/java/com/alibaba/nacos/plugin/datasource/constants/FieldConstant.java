/*
 * Copyright 1999-2022 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.plugin.datasource.constants;

/**
 * The field name constant.
 *
 * <AUTHOR>
 **/

public class FieldConstant {
    
    public static final String TENANT_ID = "tenantId";
    
    public static final String TENANT = "tenant";
    
    public static final String CONTENT = "content";
    
    public static final String GROUP_ID = "groupId";
    
    public static final String DATA_ID = "dataId";
    
    public static final String APP_NAME = "app_name";
    
    public static final String ENCRYPTED_DATA_KEY = "encrypted_data_key";
    
    public static final String START_ROW = "startRow";
    
    public static final String PAGE_SIZE = "pageSize";
    
    public static final String ID = "id";
    
    public static final String START_TIME = "startTime";
    
    public static final String END_TIME = "endTime";
    
    public static final String TAG_ARR = "tagARR";
    
    public static final String LAST_MAX_ID = "lastMaxId";
    
    public static final String DATUM_ID = "datumId";
    
    public static final String IS_IN = "isIn";
    
    public static final String MD5 = "md5";
    
    public static final String BETA_IPS = "betaIps";
    
    public static final String GMT_MODIFIED = "gmtModified";
    
    public static final String SRC_USER = "srcUser";
    
    public static final String SRC_IP = "srcIp";
    
    public static final String IDS = "ids";
    
    public static final String C_DESC = "cDesc";
    
    public static final String C_USE = "cUse";
    
    public static final String EFFECT = "effect";
    
    public static final String C_SCHEMA = "cSchema";
    
    public static final String TYPE = "type";
    
    public static final String TAG_ID = "tagId";
    
    public static final String QUOTA = "quota";
    
    public static final String MAX_SIZE = "maxSize";
    
    public static final String MAX_AGGR_COUNT = "maxAggrCount";
    
    public static final String MAX_AGGR_SIZE = "maxAggrSize";
    
    public static final String GMT_CREATE = "gmtCreate";
    
    public static final String USAGE = "usage";
    
    public static final String LIMIT_SIZE = "limitSize";
}
