/*
 * Copyright 1999-2022 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.plugin.datasource.constants;

/**
 * The table name constant.
 *
 * <AUTHOR>
 **/

public class TableConstant {
    
    public static final String CONFIG_INFO = "config_info";
    
    public static final String CONFIG_INFO_AGGR = "config_info_aggr";
    
    public static final String CONFIG_INFO_BETA = "config_info_beta";
    
    public static final String CONFIG_INFO_TAG = "config_info_tag";
    
    public static final String CONFIG_TAGS_RELATION = "config_tags_relation";
    
    public static final String GROUP_CAPACITY = "group_capacity";
    
    public static final String HIS_CONFIG_INFO = "his_config_info";
    
    public static final String TENANT_CAPACITY = "tenant_capacity";
    
    public static final String TENANT_INFO = "tenant_info";
}
