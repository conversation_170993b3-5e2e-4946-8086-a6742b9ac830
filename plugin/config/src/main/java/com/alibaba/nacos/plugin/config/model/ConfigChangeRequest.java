/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.plugin.config.model;

import com.alibaba.nacos.plugin.config.constants.ConfigChangePointCutTypes;

import java.util.HashMap;

/**
 * ConfigChangeRequest.
 *
 * <AUTHOR>
 */
public class ConfigChangeRequest {

    private ConfigChangePointCutTypes requestType;

    private HashMap<String, Object> requestArgs = new HashMap<>(8);

    public ConfigChangeRequest(ConfigChangePointCutTypes requestType) {
        this.requestType = requestType;
    }

    public ConfigChangePointCutTypes getRequestType() {
        return requestType;
    }

    public void setArg(String key, Object value) {
        requestArgs.putIfAbsent(key, value);
    }

    public Object getArg(String key) {
        return requestArgs.getOrDefault(key, null);
    }

    public HashMap<String, Object> getRequestArgs() {
        return requestArgs;
    }
}
