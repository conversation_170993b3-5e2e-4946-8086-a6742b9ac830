/*
 * Copyright 1999-2020 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.naming.pojo.instance;

import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.common.spi.NacosServiceLoader;
import com.alibaba.nacos.naming.healthcheck.RsInfo;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class BeatInfoInstanceBuilderTest {
    
    @Mock
    private HttpServletRequest request;
    
    private RsInfo beatInfo;
    
    private BeatInfoInstanceBuilder builder;
    
    @BeforeClass
    public static void setUpBeforeClass() {
        NacosServiceLoader.load(InstanceExtensionHandler.class);
    }
    
    @Before
    public void setUp() throws Exception {
        builder = BeatInfoInstanceBuilder.newBuilder();
        builder.setRequest(request);
        beatInfo = new RsInfo();
        beatInfo.setServiceName("g@@s");
        beatInfo.setCluster("c");
        beatInfo.setIp("*******");
        beatInfo.setPort(8848);
        beatInfo.setWeight(10);
        beatInfo.setMetadata(new HashMap<>());
    }
    
    @Test
    public void testBuild() {
        Instance actual = builder.setServiceName("g@@s").setBeatInfo(beatInfo).build();
        assertThat(actual.getServiceName(), is("g@@s"));
        assertThat(actual.getIp(), is("*******"));
        assertThat(actual.getPort(), is(8848));
        assertThat(actual.getClusterName(), is("c"));
        assertThat(actual.getWeight(), is(10.0));
        assertTrue(actual.isEphemeral());
        assertTrue(actual.isEnabled());
        assertTrue(actual.isHealthy());
        assertThat(actual.getInstanceId(), is("*******#8848#c#g@@s"));
        assertThat(actual.getMetadata().size(), is(1));
        assertThat(actual.getMetadata().get("mock"), is("mock"));
        verify(request).getParameter("mock");
    }
}
