/*
 *  Copyright 1999-2021 Alibaba Group Holding Ltd.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

package com.alibaba.nacos.naming.selector;

import com.alibaba.nacos.api.naming.pojo.Instance;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;
import java.util.List;

/**
 * {@link NoneSelector} unit tests.
 *
 * <AUTHOR>
 * @date 2021-08-05 19:47
 */
public class NoneSelectorTest {
    
    @Test
    public void testSelect() {
        NoneSelector<Instance> noneSelector = new NoneSelector<>();
        List<Instance> providers = Collections.emptyList();
        Assert.assertEquals(providers, noneSelector.select(providers));
    }
}
