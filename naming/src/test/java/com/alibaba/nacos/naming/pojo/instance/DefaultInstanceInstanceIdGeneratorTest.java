/*
 * Copyright 1999-2020 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.naming.pojo.instance;

import com.alibaba.nacos.api.naming.pojo.Instance;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

public class DefaultInstanceInstanceIdGeneratorTest {
    
    @Test
    public void testGenerateInstanceId() {
        final DefaultInstanceIdGenerator instanceIdGenerator = new DefaultInstanceIdGenerator();
        Instance instance = new Instance();
        instance.setServiceName("service");
        instance.setClusterName("cluster");
        instance.setIp("*******");
        instance.setPort(1000);
        assertThat(instanceIdGenerator.generateInstanceId(instance), is("*******#1000#cluster#service"));
    }
}
