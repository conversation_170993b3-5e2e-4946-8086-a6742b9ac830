/*
 * Copyright 1999-2020 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.naming.core.v2.event.publisher;

import com.alibaba.nacos.common.notify.Event;

class TestEvent extends Event {
    
    private static final long serialVersionUID = 882465625511504010L;
    
    static class TestEvent1 extends TestEvent {
        
        private static final long serialVersionUID = 4188906203345433816L;
    }
    
    static class TestEvent2 extends TestEvent {
        
        private static final long serialVersionUID = -3956191439344777407L;
    }
}
