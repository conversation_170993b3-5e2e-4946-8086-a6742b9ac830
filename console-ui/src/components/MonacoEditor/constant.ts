/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export const MONACO_OPTIONS: Object = {
  codeLens: true,
  selectOnLineNumbers: true,
  roundedSelection: false,
  readOnly: false,
  lineNumbersMinChars: true,
  theme: 'vs-dark-enhanced',
  wordWrapColumn: 120,
  folding: true,
  showFoldingControls: 'always',
  wordWrap: 'wordWrapColumn',
  cursorStyle: 'line',
  automaticLayout: true,
};
