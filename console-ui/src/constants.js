/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export const LANGUAGE_KEY = 'docsite_language';
export const LANGUAGE_SWITCH = 'LANGUAGE_SWITCH';

// TODO: 后端暂时没有统一成功失败标记
// export const SUCCESS_RESULT_CODE = 'SUCCESS';

export const REDUX_DEVTOOLS = '__REDUX_DEVTOOLS_EXTENSION__';

export const GET_STATE = 'GET_STATE';

export const GET_NOTICE = 'GET_NOTICE';

export const GET_SUBSCRIBERS = 'GET_SUBSCRIBERS';
export const REMOVE_SUBSCRIBERS = 'REMOVE_SUBSCRIBERS';

export const UPDATE_USER = 'UPDATE_USER';
export const SIGN_IN = 'SIGN_IN';
export const USER_LIST = 'USER_LIST';

export const ROLE_LIST = 'ROLE_LIST';

export const PERMISSIONS_LIST = 'PERMISSIONS_LIST';

export const GET_NAMESPACES = 'GET_NAMESPACES';

export const GET_CONFIGURATION = 'GET_CONFIGURATION';

export const GLOBAL_PAGE_SIZE_LIST = [10, 20, 30, 50, 100];

export const LOGINPAGE_ENABLED = 'docsite_loginpage';

export const THEME = 'setting_theme';

export const SERVER_GUIDE = 'SERVER_GUIDE';
