/*!
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.header-container-primary {
  background: #252a2f;
}

.header-container-normal {
  background-color: #fff;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.08);
}

.header-container .header-body {
  /* max-width: 1280px; */
  width: 100%;
  margin: 0 auto;
  height: 66px;
  line-height: 66px;
}

.header-container .header-body .logo {
  margin-left: 40px;
  width: 96px;
  vertical-align: sub;
}

.header-container .header-body .header-menu {
  float: right;
}

.header-container .header-body .header-menu .header-menu-toggle {
  display: none;
  width: 19px;
  margin-right: 40px;
  margin-top: 18px;
  cursor: pointer;
}

.header-container .header-body ul {
  padding: 0;
  margin: 0;
}

.header-container .header-body li {
  display: inline-block;
  margin-right: 40px;
}

.header-container .header-body .menu-item {
  font-family: Avenir-Heavy;
  font-size: 14px;
}

.header-container .header-body .menu-item-primary a {
  color: #fff;
  opacity: 0.6;
  font-family: Avenir-Medium;
}

.header-container .header-body .menu-item-primary:hover a {
  opacity: 1;
}

.header-container .header-body .menu-item-primary-active a {
  opacity: 1;
}

.header-container .header-body .menu-item-normal a {
  color: #333;
  opacity: 0.6;
  font-family: Avenir-Medium;
}

.header-container .header-body .menu-item-normal:hover a {
  opacity: 1;
}

.header-container .header-body .menu-item-normal-active a {
  opacity: 1;
}

.header-container .header-body .language-switch {
  float: right;
  display: inline-block;
  box-sizing: border-box;
  width: 24px;
  height: 24px;
  line-height: 20px;
  margin-top: 21px;
  margin-right: 40px;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  opacity: 0.6;
}

.header-container .header-body .logout {
  float: right;
  color: #fff;
  opacity: 0.6;
  font-family: Avenir-Medium;
  margin-right: 40px;
}

.header-container .header-body .language-switch:hover {
  opacity: 1;
}

.header-container .header-body .language-switch-primary {
  border: 1px solid #fff;
  color: #fff;
}

.header-container .header-body .language-switch-normal {
  border: 1px solid #333;
  color: #333;
}

@media screen and (max-width: 640px) {
  .header-container .header-body .logo {
    margin-left: 20px;
  }

  .header-container .header-body .language-switch {
    margin-right: 20px;
  }

  .header-container .header-body .header-menu ul {
    display: none;
  }

  .header-container .header-body .header-menu .header-menu-toggle {
    display: inline-block;
    margin-right: 20px;
  }

  .header-container .header-body .header-menu-open ul {
    background-color: #f8f8f8;
    display: inline-block;
    position: absolute;
    right: 0;
    top: 66px;
    z-index: 100;
  }

  .header-container .header-body .header-menu-open li {
    width: 200px;
    display: list-item;
    padding-left: 30px;
    list-style: none;
    line-height: 40px;
    margin-right: 0;
  }

  .header-container .header-body .header-menu-open li a {
    color: #333;
    display: inline-block;
    width: 100%;
  }

  .header-container .header-body .header-menu-open li:hover {
    background: #2e3034;
  }

  .header-container .header-body .header-menu-open li:hover a {
    color: #fff;
    opactiy: 1;
  }

  .header-container .header-body .header-menu-open .menu-item-primary-active,
  .header-container .header-body .header-menu-open .menu-item-normal-active {
    background: #2e3034;
  }

  .header-container .header-body .header-menu-open .menu-item-primary-active a,
  .header-container .header-body .header-menu-open .menu-item-normal-active a {
    color: #fff;
    opactiy: 1;
  }
}

.bone {
  width: 24px;
  height: 2px;
  position: relative;
}

.bone::before {
  position: absolute;
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  left: 0;
  top: -2px;
}

.bone::after {
  position: absolute;
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  right: 0;
  top: -2px;
}

.bone-dark {
  background-color: #1161f6;
}

.bone-dark::before {
  background-color: #1161f6;
}

.bone-dark::after {
  background-color: #1161f6;
}

.bone-light {
  background-color: #fff;
  opacity: 0.8;
}

.bone-light::before {
  background-color: #fff;
  opacity: 0.8;
}

.bone-light::after {
  background-color: #fff;
  opacity: 0.8;
}

.footer-container {
  background: #f8f8f8;
}

.footer-container .footer-body {
  max-width: 1280px;
  margin: 0 auto;
  padding: 40px 40px 0;
}

@media screen and (max-width: 640px) {
  .footer-container .footer-body {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.footer-container .footer-body img {
  display: block;
  width: 125px;
  height: 26px;
  margin-bottom: 40px;
}

.footer-container .footer-body .cols-container .col {
  display: inline-block;
  box-sizing: border-box;
  vertical-align: top;
}

.footer-container .footer-body .cols-container .col-12 {
  width: 50%;
  padding-right: 125px;
}

.footer-container .footer-body .cols-container .col-6 {
  width: 25%;
}

.footer-container .footer-body .cols-container h3 {
  font-family: Avenir-Heavy;
  font-size: 18px;
  color: #333;
  line-height: 18px;
  margin-bottom: 20px;
}

.footer-container .footer-body .cols-container p {
  font-family: Avenir-Medium;
  font-size: 12px;
  color: #999;
  line-height: 18px;
}

.footer-container .footer-body .cols-container dl {
  font-family: Avenir-Heavy;
  line-height: 18px;
}

.footer-container .footer-body .cols-container dt {
  font-weight: bold;
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
}

.footer-container .footer-body .cols-container dd {
  padding: 0;
  margin: 0;
}

.footer-container .footer-body .cols-container dd a {
  text-decoration: none;
  display: block;
  font-size: 14px;
  color: #999;
  margin: 10px 0;
}

.footer-container .footer-body .cols-container dd a:hover {
  color: #2e3034;
}

.footer-container .footer-body .copyright {
  margin-top: 44px;
  border-top: 1px solid #ccc;
  min-height: 60px;
  line-height: 20px;
  text-align: center;
  font-family: Avenir-Medium;
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.footer-container .footer-body .copyright span {
  display: inline-block;
  margin: 0 auto;
}

@media screen and (max-width: 640px) {
  .footer-container .footer-body .cols-container .col {
    width: 100%;
    text-align: center;
    padding: 0;
  }
}

.button {
  box-sizing: border-box;
  display: inline-block;
  height: 48px;
  line-height: 48px;
  min-width: 140px;
  font-family: Avenir-Heavy;
  font-size: 16px;
  color: #fff;
  text-align: center;
  border-radius: 4px;
  text-decoration: none;
}

.button-primary {
  background: #4190ff;
}

.button-normal {
  background: transparent;
  border: 1px solid #fff;
}

@charset "UTF-8";

@font-face {
  font-family: octicons-link;
  src: url(data:font/woff;charset=utf-8;base64,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) format('woff');
}

.markdown-body {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  line-height: 1.5;
  color: #24292e;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.markdown-body .pl-c {
  color: #6a737d;
}

.markdown-body .pl-c1,
.markdown-body .pl-s .pl-v {
  color: #005cc5;
}

.markdown-body .pl-e,
.markdown-body .pl-en {
  color: #6f42c1;
}

.markdown-body .pl-smi,
.markdown-body .pl-s .pl-s1 {
  color: #24292e;
}

.markdown-body .pl-ent {
  color: #22863a;
}

.markdown-body .pl-k {
  color: #d73a49;
}

.markdown-body .pl-s,
.markdown-body .pl-pds,
.markdown-body .pl-s .pl-pse .pl-s1,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sre,
.markdown-body .pl-sr .pl-sra {
  color: #032f62;
}

.markdown-body .pl-v,
.markdown-body .pl-smw {
  color: #e36209;
}

.markdown-body .pl-bu {
  color: #b31d28;
}

.markdown-body .pl-ii {
  color: #fafbfc;
  background-color: #b31d28;
}

.markdown-body .pl-c2 {
  color: #fafbfc;
  background-color: #d73a49;
}

.markdown-body .pl-c2::before {
  content: '^M';
}

.markdown-body .pl-sr .pl-cce {
  font-weight: bold;
  color: #22863a;
}

.markdown-body .pl-ml {
  color: #735c0f;
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
  font-weight: bold;
  color: #005cc5;
}

.markdown-body .pl-mi {
  font-style: italic;
  color: #24292e;
}

.markdown-body .pl-mb {
  font-weight: bold;
  color: #24292e;
}

.markdown-body .pl-md {
  color: #b31d28;
  background-color: #ffeef0;
}

.markdown-body .pl-mi1 {
  color: #22863a;
  background-color: #f0fff4;
}

.markdown-body .pl-mc {
  color: #e36209;
  background-color: #ffebda;
}

.markdown-body .pl-mi2 {
  color: #f6f8fa;
  background-color: #005cc5;
}

.markdown-body .pl-mdr {
  font-weight: bold;
  color: #6f42c1;
}

.markdown-body .pl-ba {
  color: #586069;
}

.markdown-body .pl-sg {
  color: #959da5;
}

.markdown-body .pl-corl {
  text-decoration: underline;
  color: #032f62;
}

.markdown-body .octicon {
  display: inline-block;
  vertical-align: text-top;
  fill: currentColor;
}

.markdown-body a {
  background-color: transparent;
}

.markdown-body a:active,
.markdown-body a:hover {
  outline-width: 0;
}

.markdown-body strong {
  font-weight: inherit;
}

.markdown-body strong {
  font-weight: bolder;
}

.markdown-body h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.markdown-body img {
  border-style: none;
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre {
  font-family: monospace, monospace;
  font-size: 1em;
}

.markdown-body hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

.markdown-body input {
  font: inherit;
  margin: 0;
}

.markdown-body input {
  overflow: visible;
}

.markdown-body [type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}

.markdown-body * {
  box-sizing: border-box;
}

.markdown-body input {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.markdown-body a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-body a:hover {
  color: #0366d6;
  text-decoration: underline;
}

.markdown-body strong {
  font-weight: 600;
}

.markdown-body hr {
  height: 0;
  margin: 15px 0;
  overflow: hidden;
  background: transparent;
  border: 0;
  border-bottom: 1px solid #dfe2e5;
}

.markdown-body hr::before {
  display: table;
  content: '';
}

.markdown-body hr::after {
  display: table;
  clear: both;
  content: '';
}

.markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
}

.markdown-body td,
.markdown-body th {
  padding: 0;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body h1 {
  font-size: 32px;
  font-weight: 600;
}

.markdown-body h2 {
  font-size: 24px;
  font-weight: 600;
}

.markdown-body h3 {
  font-size: 20px;
  font-weight: 600;
}

.markdown-body h4 {
  font-size: 16px;
  font-weight: 600;
}

.markdown-body h5 {
  font-size: 14px;
  font-weight: 600;
}

.markdown-body h6 {
  font-size: 12px;
  font-weight: 600;
}

.markdown-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.markdown-body blockquote {
  margin: 0;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body ol ol,
.markdown-body ul ol {
  list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.markdown-body dd {
  margin-left: 0;
}

.markdown-body code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 12px;
}

.markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 12px;
}

.markdown-body .octicon {
  vertical-align: text-bottom;
}

.markdown-body .pl-0 {
  padding-left: 0 !important;
}

.markdown-body .pl-1 {
  padding-left: 4px !important;
}

.markdown-body .pl-2 {
  padding-left: 8px !important;
}

.markdown-body .pl-3 {
  padding-left: 16px !important;
}

.markdown-body .pl-4 {
  padding-left: 24px !important;
}

.markdown-body .pl-5 {
  padding-left: 32px !important;
}

.markdown-body .pl-6 {
  padding-left: 40px !important;
}

.markdown-body::before {
  display: table;
  content: '';
}

.markdown-body::after {
  display: table;
  clear: both;
  content: '';
}

.markdown-body>*:first-child {
  margin-top: 0 !important;
}

.markdown-body>*:last-child {
  margin-bottom: 0 !important;
}

.markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.markdown-body .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.markdown-body .anchor:focus {
  outline: none;
}

.markdown-body p,
.markdown-body blockquote,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl,
.markdown-body table,
.markdown-body pre {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-body blockquote> :first-child {
  margin-top: 0;
}

.markdown-body blockquote> :last-child {
  margin-bottom: 0;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 10px;
  color: #444d56;
  vertical-align: middle;
  background-color: #fafbfc;
  border: solid 1px #c6cbd1;
  border-bottom-color: #959da5;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #959da5;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 .octicon-link,
.markdown-body h2 .octicon-link,
.markdown-body h3 .octicon-link,
.markdown-body h4 .octicon-link,
.markdown-body h5 .octicon-link,
.markdown-body h6 .octicon-link {
  color: #1b1f23;
  vertical-align: middle;
  visibility: hidden;
}

.markdown-body h1:hover .anchor,
.markdown-body h2:hover .anchor,
.markdown-body h3:hover .anchor,
.markdown-body h4:hover .anchor,
.markdown-body h5:hover .anchor,
.markdown-body h6:hover .anchor {
  text-decoration: none;
}

.markdown-body h1:hover .anchor .octicon-link,
.markdown-body h2:hover .anchor .octicon-link,
.markdown-body h3:hover .anchor .octicon-link,
.markdown-body h4:hover .anchor .octicon-link,
.markdown-body h5:hover .anchor .octicon-link,
.markdown-body h6:hover .anchor .octicon-link {
  visibility: visible;
}

.markdown-body h1 {
  padding-bottom: 0.3em;
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
}

.markdown-body h2 {
  padding-bottom: 0.3em;
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
}

.markdown-body h3 {
  font-size: 1.25em;
}

.markdown-body h4 {
  font-size: 1em;
}

.markdown-body h5 {
  font-size: 0.875em;
}

.markdown-body h6 {
  font-size: 0.85em;
  color: #6a737d;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
}

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body li {
  word-wrap: break-all;
}

.markdown-body li>p {
  margin-top: 16px;
}

.markdown-body li+li {
  margin-top: 0.25em;
}

.markdown-body dl {
  padding: 0;
}

.markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: 600;
}

.markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
}

.markdown-body table th {
  font-weight: 600;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.markdown-body img {
  max-width: 100%;
  box-sizing: content-box;
  background-color: #fff;
}

.markdown-body img[align='right'] {
  padding-left: 20px;
}

.markdown-body img[align='left'] {
  padding-right: 20px;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

.markdown-body pre {
  word-wrap: normal;
}

.markdown-body pre>code {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.markdown-body .highlight {
  margin-bottom: 16px;
}

.markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.markdown-body .highlight pre,
.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-body pre code {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.markdown-body .full-commit .btn-outline:not(:disabled):hover {
  color: #005cc5;
  border-color: #005cc5;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font: 11px 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  line-height: 10px;
  color: #444d56;
  vertical-align: middle;
  background-color: #fafbfc;
  border: solid 1px #d1d5da;
  border-bottom-color: #c6cbd1;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #c6cbd1;
}

.markdown-body :checked+.radio-label {
  position: relative;
  z-index: 1;
  border-color: #0366d6;
}

.markdown-body .task-list-item {
  list-style-type: none;
}

.markdown-body .task-list-item+.task-list-item {
  margin-top: 3px;
}

.markdown-body .task-list-item input {
  margin: 0 0.2em 0.25em -1.6em;
  vertical-align: middle;
}

.markdown-body hr {
  border-bottom-color: #eee;
}

/* 代码高亮 */
/*
 * Visual Studio 2015 dark style
 * Author: Nicolas LLOBERA <<EMAIL>>
 */
.markdown-body pre code {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #1e1e1e;
  color: #dcdcdc;
}

.hljs-keyword,
.hljs-literal,
.hljs-symbol,
.hljs-name {
  color: #569cd6;
}

.hljs-link {
  color: #569cd6;
  text-decoration: underline;
}

.hljs-built_in,
.hljs-type {
  color: #4ec9b0;
}

.hljs-number,
.hljs-class {
  color: #b8d7a3;
}

.hljs-string,
.hljs-meta-string {
  color: #d69d85;
}

.hljs-regexp,
.hljs-template-tag {
  color: #9a5334;
}

.hljs-subst,
.hljs-function,
.hljs-title,
.hljs-params,
.hljs-formula {
  color: #dcdcdc;
}

.hljs-comment,
.hljs-quote {
  color: #57a64a;
  font-style: italic;
}

.hljs-doctag {
  color: #608b4e;
}

.hljs-meta,
.hljs-meta-keyword,
.hljs-tag {
  color: #9b9b9b;
}

.hljs-variable,
.hljs-template-variable {
  color: #bd63c5;
}

.hljs-attr,
.hljs-attribute,
.hljs-builtin-name {
  color: #9cdcfe;
}

.hljs-section {
  color: gold;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/*.hljs-code {
  font-family:'Monospace';
}*/
.hljs-bullet,
.hljs-selector-tag,
.hljs-selector-id,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #d7ba7d;
}

.hljs-addition {
  background-color: #144212;
  display: inline-block;
  width: 100%;
}

.hljs-deletion {
  background-color: #600;
  display: inline-block;
  width: 100%;
}

* {
  padding: 0;
  margin: 0;
}

a {
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
}

@keyframes slashStar {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.home-page .top-section {
  position: relative;
  height: 720px;
}

.home-page .top-section .animation {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1be1f6;
}

.home-page .top-section .animation1 {
  left: 15%;
  top: 70%;
  animation: slashStar 2s ease-in-out 0.3s infinite;
}

.home-page .top-section .animation2 {
  left: 34%;
  top: 35%;
  animation: slashStar 2s ease-in-out 1.2s infinite;
}

.home-page .top-section .animation3 {
  left: 53%;
  top: 20%;
  animation: slashStar 2s ease-in-out 0.5s infinite;
}

.home-page .top-section .animation4 {
  left: 72%;
  top: 64%;
  animation: slashStar 2s ease-in-out 0.8s infinite;
}

.home-page .top-section .animation5 {
  left: 87%;
  top: 30%;
  animation: slashStar 2s ease-in-out 1.5s infinite;
}

.home-page .top-section .vertical-middle {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
}

.home-page .top-section .product-logo {
  display: block;
  width: 257px;
  height: 50px;
  margin: 0 auto;
}

.home-page .top-section .product-desc {
  opacity: 0.8;
  font-family: Avenir-Medium;
  font-size: 24px;
  color: #fff;
  max-width: 780px;
  margin: 12px auto 30px;
  text-align: center;
}

.home-page .top-section .button-area {
  text-align: center;
}

.home-page .top-section .button-area .button:first-child {
  margin-right: 20px;
}

.home-page .top-section .version-note {
  text-align: center;
  margin: 22px 0 10px;
}

.home-page .top-section .version-note a {
  text-decoration: none;
  display: inline-block;
  font-family: Avenir-Heavy;
  font-size: 14px;
  color: #fff;
  text-align: center;
  background: #46484b;
  border-radius: 2px;
  line-height: 24px;
  padding: 0 6px;
  margin-right: 10px;
}

.home-page .top-section .release-date {
  font-family: Avenir-Medium;
  font-size: 12px;
  color: #999;
  text-align: center;
}

.home-page .function-section {
  max-width: 832px;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 82px 0;
}

.home-page .function-section h3 {
  font-family: Avenir-Heavy;
  font-size: 36px;
  text-align: center;
  font-weight: 400;
}

.home-page .function-section .bone {
  margin: 0 auto 45px;
}

.home-page .function-section .func-item {
  margin-bottom: 30px;
  position: relative;
}

.home-page .function-section .func-item .col {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin: 0 auto;
  width: 50%;
  max-width: 750px;
  min-height: 325px;
}

.home-page .function-section .func-item .col img {
  width: 325px;
}

.home-page .function-section .func-item .col h4 {
  font-weight: 400;
  font-family: Avenir-Heavy;
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.home-page .function-section .func-item .col p {
  opacity: 0.8;
  font-family: Avenir-Medium;
  font-size: 18px;
  color: #999;
  margin: 0;
}

.home-page .function-section .func-item .img {
  display: inline-block;
  text-align: center;
}

@media screen and (max-width: 830px) {
  .home-page .function-section .func-item {
    text-align: center;
  }

  .home-page .function-section .func-item .col {
    width: 100%;
  }

  .home-page .function-section .func-item .img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.1;
  }
}

.home-page .feature-section {
  background: #2e3034;
}

.home-page .feature-section .feature-section-body {
  max-width: 1280px;
  margin: 0 auto;
  position: relative;
  padding: 80px 40px;
  color: #fff;
}

.home-page .feature-section .feature-section-body h3 {
  font-family: Avenir-Heavy;
  font-size: 36px;
  text-align: center;
  margin: 0;
  font-weight: 400;
}

.home-page .feature-section .feature-section-body .bone {
  margin: 0 auto 45px;
}

.home-page .feature-section .feature-section-body .feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item {
  vertical-align: top;
  display: inline-block;
  margin-bottom: 48px;
  width: 50%;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item ul {
  list-style: disc;
  padding-left: 14px;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item ul li {
  font-family: Avenir-Medium;
  font-size: 14px;
  color: #999;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item img {
  vertical-align: top;
  width: 34px;
  margin-right: 20px;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item div {
  display: inline-block;
  width: 80%;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item div h4 {
  font-family: Avenir-Heavy;
  font-size: 20px;
  margin: 5px 0 20px 0;
}

.home-page .feature-section .feature-section-body .feature-list .feature-list-item div p {
  font-family: Avenir-Medium;
  font-size: 14px;
  line-height: 20px;
  color: #999;
}

@media screen and (max-width: 768px) {
  .home-page .feature-section .feature-section-body .feature-list .feature-list-item {
    width: 100%;
  }
}

@media screen and (max-width: 640px) {
  .home-page .feature-section-body {
    padding-left: 20px;
    padding-right: 20px;
  }
}

.product-nav-list li.selected a {
  background-color: #f4f6f8;
}

.main-container {
  height: calc(100vh - 66px);
  background-color: #fff;

  .right-panel {
    background-color: #fff;
    width: calc(100% - 180px);
    padding: 12px 32px;
    overflow: scroll;
  }

  .nav-title {
    margin: 0;
    // background-color: #E4F3FE;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    height: 60px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    border-bottom: var(--shell-brand-navigation-ver-divider-size, 1px) var(--shell-brand-navigation-ver-divider-style, solid) var(--shell-brand-navigation-ver-divider-color, #EEEEEE);
    display: flex;
    justify-content: center;
    align-items: center;

    span {
      margin-left: 5px;
    }
  }

  .nav-mode {
    margin: 0;
    // background-color: #E4F3FE;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    height: 45px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    border-bottom: var(--shell-brand-navigation-ver-divider-size, 1px) var(--shell-brand-navigation-ver-divider-style, solid) var(--shell-brand-navigation-ver-divider-color, #EEEEEE);
    display: flex;
    justify-content: center;
    align-items: center;

    span {
      margin-left: 5px;
    }
  }

  .nav-menu {
    padding: 0;
    background: transparent;
    border: 0;
    line-height: 40px;

    div.next-menu-item,
    .first-menu>.next-menu-item-inner {
      color: #333;
    }

    .next-menu-item-inner {
      height: 40px;
      color: #666;
    }

    .current-path {
      background-color: #f2f3f7;
    }
  }

  .go-back {
    text-align: center;
    color: rgb(84, 100, 120);
    font-size: 20px;
    font-weight: bold;
    padding: 10px 0;
    margin-top: 14px;
    cursor: pointer;
  }
}

.enable-dialog {
  .next-dialog-body {
    padding-top: 10px;
  }

  .next-dialog-close {
    display: none;
  }
}
