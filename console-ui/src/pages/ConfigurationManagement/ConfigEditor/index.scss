/*!
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.config-editor {
  padding: 10px;
  .func-title {
    overflow: hidden;
    height: 50px;
    width: 100%;
    font-weight: 500;
    margin-bottom: 9px;
    font-size: 18px;
    line-height: 36px;
    color: #73777a;
  }
  .form {
    display: table;
    .next-form-item {
      display: table-row;
      .next-form-item-label {
        white-space: nowrap;
        word-break: keep-all;
      }
      .next-form-item-control,
      .next-select {
        width: 100%;
      }
      .next-form-item-label,
      .next-form-item-control {
        display: table-cell;
      }
    }
    .next-form-item-control {
      padding-bottom: 12px;
    }
    .next-checkbox-label {
      color: #73777a;
      font-weight: normal;
    }
    .next-radio-label {
      color: #73777a;
    }
    .switch {
      color: #33cde5;
      cursor: pointer;
      user-select: none;
    }
    .help-label > * {
      display: inline-block;
    }
    .help-label > i {
      color: #1dc11d;
      margin: 0 0.25em;
    }
  }
  .button-list {
    text-align: right;
    button {
      margin-left: 1em;
      font-size: 14px;
    }
  }
  .editor-full-screen {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  .editor-normal {
    clear: both;
  }
}
