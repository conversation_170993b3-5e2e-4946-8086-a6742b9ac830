/*!
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.service-detail {
  .header-btn {
    float: right;
    margin-left: 20px;
  }
  .edit-btn {
    margin-right: 10px;
  }
  .next-form-item {
    margin-bottom: 10px;
  }
  .loading {
    position: relative;
    width: 100%;
  }
  .pagination {
    float: right;
    margin-top: 15px;
  }
  .cluster-card {
    margin-bottom: 30px;
  }
  .inner-card {
    margin-bottom: 10px;
  }
}

.service-detail-edit-dialog,
.instance-edit-dialog,
.cluster-edit-dialog {
  .next-form-item {
    margin-bottom: 10px;
  }
  .next-col-fixed-12 {
    flex: 1;
  }
  .next-switch-off {
    background-color: #f2f3f7;
    border-color: #c4c6cf;
  }
  .in-text,
  .in-select {
    width: 120px;
  }
}

.service-detail-edit-dialog {
  width: 600px;
}

.full-width {
  width: 100%;
}
