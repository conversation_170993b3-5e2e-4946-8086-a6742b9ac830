/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export const DIALOG_FORM_LAYOUT = {
  labelCol: { fixedSpan: 6 },
  wrapperCol: { span: 18 },
};

export const HEALTHY_COLOR_MAPPING = {
  true: 'green',
  false: 'red',
};

export const MONACO_READONLY_OPTIONS = {
  readOnly: true,
};

export const METADATA_SEPARATOR = ',';

export const METADATA_ENTER = '\r\n';
