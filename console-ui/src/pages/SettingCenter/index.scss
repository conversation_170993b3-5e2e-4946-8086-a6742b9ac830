/*!
 * Copyright 1999-2023 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

.setting-box {
  margin-top: 15px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #f6f7fb;
  border-radius: 0;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.text-box {
  height: 140px;
  display: flex;
}

.setting-checkbox {
  flex: 1;
  height: 100px;
  box-sizing: border-box;
}

.setting-span {
  font-size: 18px;
  font-weight: normal;
  margin-bottom: 15px;
}
