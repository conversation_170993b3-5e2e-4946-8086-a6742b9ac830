{"name": "console-ui", "version": "1.0.0", "description": "console ui", "main": "index.js", "scripts": {"start": "cross-env NODE_ENV=development webpack-dev-server --config build/webpack.dev.conf.js", "build": "cross-env NODE_ENV=production webpack --config build/webpack.prod.conf.js && node build/copyFile.js", "eslint": "eslint --ext .js src/", "eslint-fix": "eslint  --ext .js --fix src/"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,css,less}": ["prettier --write", "git add"]}, "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/alibaba/nacos.git"}, "devDependencies": {"@alifd/next-theme-loader": "^1.3.2", "@babel/cli": "^7.7.7", "@babel/core": "^7.7.7", "@babel/plugin-proposal-decorators": "^7.7.4", "@babel/preset-env": "^7.7.7", "@babel/runtime": "^7.7.7", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.4", "babel-plugin-import": "^1.13.0", "babel-preset-react-app": "^9.1.0", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^5.1.1 ", "cross-env": "^6.0.3", "css-loader": "^3.4.0", "eslint": "^6.8.0", "eslint-config-ali": "^9.0.2", "eslint-config-prettier": "^6.8.0", "eslint-loader": "^3.0.3", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-react": "^7.17.0", "eslint-plugin-react-hooks": "^2.3.0", "file-loader": "^5.0.2", "html-webpack-plugin": "^3.2.0", "husky": "^3.1.0", "lint-staged": "^9.5.0", "mini-css-extract-plugin": "^0.9.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "prettier": "1.19.1", "sass": "^1.54.0", "sass-loader": "^8.0.0", "style-loader": "^1.1.2", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^3.0.0", "webpack": "^4.43.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0"}, "dependencies": {"@alifd/next": "^1.26.24", "@alifd/theme-design-pro": "0.x", "@iarna/toml": "^3.0.0", "axios": "^0.21.1", "js-yaml": "^4.1.0", "moment": "^2.23.0", "prop-types": "^15.6.2", "qs": "^6.8.2", "react": "^16.12.0", "react-dom": "^16.12.0", "react-icons": "^4.12.0", "react-redux": "^7.1.3", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "react-router-redux": "^4.0.8", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "core-js": "^3.6.4"}}